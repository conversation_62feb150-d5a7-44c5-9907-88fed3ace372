package predict

import (
	"errors"
	"log"
	"rtb_model_server/common/domob_thrift/predict_model_server"
	"rtb_model_server/internal/zaplog"
	"sync"
	"time"

	"git.apache.org/thrift.git/lib/go/thrift"
	"go.uber.org/zap"
)

// ThriftConnection 表示一个Thrift连接
type ThriftConnection struct {
	transport *thrift.TSocket
	client    *predict_model_server.PredictModelServerClient
	inUse     bool
	lastUsed  time.Time
}

// ConnectionPool 连接池管理器
type ConnectionPool struct {
	connPool     []*ThriftConnection
	poolSize     int
	maxIdleConns int
	connTimeout  time.Duration
	addr         string
	mu           sync.Mutex
}

// NewConnectionPool 创建新的连接池
func NewConnectionPool(addr string, poolSize, maxIdleConns int, connTimeout time.Duration) *ConnectionPool {
	p := &ConnectionPool{
		poolSize:     poolSize,
		maxIdleConns: maxIdleConns,
		connTimeout:  connTimeout,
		addr:         addr,
		connPool:     make([]*ThriftConnection, 0, poolSize),
	}

	// 初始化连接池
	var errorCount int
	var lastError error
	for i := 0; i < maxIdleConns; i++ {
		conn, err := p.createConnection()
		if err != nil {
			lastError = err
			errorCount++
			zaplog.Logger.Error("failed to create initial connection", zap.Int("index", i), zap.Error(err))
			continue
		}
		p.connPool = append(p.connPool, conn)
	}
	if errorCount > 0 {
		log.Println("[Warn] init predict server thrift connection error count: ", errorCount, "; last error: ", lastError)
	}

	return p
}

// createConnection 创建新的连接
func (p *ConnectionPool) createConnection() (*ThriftConnection, error) {
	transport, err := thrift.NewTSocket(p.addr)
	if err != nil {
		return nil, err
	}

	// 设置连接超时
	transport.SetTimeout(p.connTimeout)

	// 打开连接
	if err := transport.Open(); err != nil {
		return nil, err
	}

	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()
	client := predict_model_server.NewPredictModelServerClientFactory(transport, protocolFactory)

	return &ThriftConnection{
		transport: transport,
		client:    client,
		inUse:     false,
		lastUsed:  time.Now(),
	}, nil
}

// GetConnection 从连接池获取连接
func (p *ConnectionPool) GetConnection() (*ThriftConnection, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 查找可用的空闲连接
	zaplog.Logger.Info("get connection from pool")
	for _, conn := range p.connPool {
		if !conn.inUse {
			// 检查连接是否还有效
			if conn.transport.IsOpen() {
				conn.inUse = true
				conn.lastUsed = time.Now()
				return conn, nil
			} else {
				// 连接已断开，重新创建
				newConn, err := p.createConnection()
				if err != nil {
					continue
				}
				*conn = *newConn
				conn.inUse = true
				zaplog.Logger.Info("get connection from pool, create new connection")
				return conn, nil
			}
		}
	}

	zaplog.Logger.Info("get connection from pool, no idle connection")
	// 如果没有空闲连接且池未满，创建新连接
	if len(p.connPool) < p.poolSize {
		conn, err := p.createConnection()
		zaplog.Logger.Info("get connection from pool, create new connection")
		if err != nil {
			zaplog.Logger.Error("get connection from pool, create new connection error")
			return nil, err
		}
		conn.inUse = true
		p.connPool = append(p.connPool, conn)
		zaplog.Logger.Info("get connection from pool, create new connection success")
		return conn, nil
	}

	return nil, errors.New("connection pool exhausted")
}

// ReleaseConnection 释放连接回连接池
func (p *ConnectionPool) ReleaseConnection(conn *ThriftConnection) {
	p.mu.Lock()
	defer p.mu.Unlock()

	conn.inUse = false
	conn.lastUsed = time.Now()
}

// Close 关闭连接池中的所有连接
func (p *ConnectionPool) Close() {
	p.mu.Lock()
	defer p.mu.Unlock()

	for _, conn := range p.connPool {
		if conn.transport != nil && conn.transport.IsOpen() {
			conn.transport.Close()
		}
	}
	p.connPool = nil
}

// GetPoolStats 获取连接池统计信息
func (p *ConnectionPool) GetPoolStats() (total, inUse, idle int) {
	p.mu.Lock()
	defer p.mu.Unlock()

	total = len(p.connPool)
	for _, conn := range p.connPool {
		if conn.inUse {
			inUse++
		} else {
			idle++
		}
	}
	return
}
