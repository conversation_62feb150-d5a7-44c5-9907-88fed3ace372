package adindex

import (
	"fmt"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/internal/zaplog"

	"go.uber.org/zap"
)

// StrategyFilter 默认的Strategy过滤器
func (i *AdIndexManager) StrategyFilter(strategy IndexableStrategy) bool {
	// 示例过滤逻辑：过滤掉状态异常的strategy
	if strategy.RTBStrategy == nil {
		zaplog.Logger.Warn("strategy is nil, filtering out", zap.Int32("id", strategy.GetId()))
		return false
	}

	// 检查各个层级的状态
	if strategy.PauseStatus != 0 || strategy.Status != 0 {
		return false
	}

	// 将所使用的资源包进行缓存，在加载资源包的时候只加载这些使用的，避免加载所有资源包，浪费内存
	for _, resource := range strategy.ResourceTarget {
		dictResourceUsed.Store(resource, true)
	}

	// 存储日预算
	dictStrategyBudget.Store(strategy.Id, strategy.DailyBudget)

	//
	// if strategy.Id == 166733 {
	// 	log.Println("StrategyFilter->", strategy.Id, strategy.Name, strategy.ResourceTarget)
	// }

	// 可以添加更多过滤条件，例如：
	// - 过滤掉已暂停的strategy
	// - 过滤掉预算耗尽的strategy
	// - 过滤掉无效的strategy配置

	return true
}

func (i *AdIndexManager) GetStrategyBudget(strategyId int32) (int64, bool) {
	if budget, ok := dictStrategyBudget.Load(strategyId); ok {
		return budget.(int64), true
	}
	return 0, false
}

// LoadStrategy 加载Strategy数据，使用默认过滤器
func (i *AdIndexManager) LoadStrategy() (int, error) {
	return i.LoadStrategyWithFilter(i.StrategyFilter)
}

// LoadStrategyWithFilter 加载Strategy数据，使用自定义过滤器
func (i *AdIndexManager) LoadStrategyWithFilter(filter FilterFunc[IndexableStrategy]) (int, error) {
	versionedPath := i.getVersionedFilePath(i.config.AdIndexPath.AdStrategy)
	return LoadAdIndexData(versionedPath, "strategy", func() IndexableStrategy {
		return IndexableStrategy{RTBStrategy: &rtb_adinfo_types.RTBStrategy{}}
	}, filter)
}

func (i *AdIndexManager) GetStrategy(strategyId int32) (*rtb_adinfo_types.RTBStrategy, error) {
	zaplog.Logger.Info("AdIndexManager GetStrategy", zap.Int32("strategy_id", strategyId))
	if data, ok := dictAdStrategy.Load(strategyId); ok {
		zaplog.Logger.Info("AdIndexManager GetStrategy found in index dict")
		return data.(IndexableStrategy).RTBStrategy, nil
	}
	zaplog.Logger.Info("AdIndexManager GetStrategy not found in index dict")
	return nil, fmt.Errorf("strategy %d not found in index dict", strategyId)
}

func (i *AdIndexManager) ResourceTargetPkgUsed(resourceID int32) bool {
	if _, ok := dictResourceUsed.Load(resourceID); ok {
		return true
	}
	return false
}

func (i *AdIndexManager) ResourceTargetPkgNumber() int {
	var count int
	dictResourceUsed.Range(func(key, value any) bool {
		count++
		return true
	})
	return count
}

func (i *AdIndexManager) GetAllResourceTarget() []int32 {
	var resourceTargetList []int32
	dictResourceUsed.Range(func(key, value any) bool {
		resourceTargetList = append(resourceTargetList, key.(int32))
		return true
	})
	return resourceTargetList
}
